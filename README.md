# Pentester AI Agent

A sophisticated AI agent system designed for cybersecurity and penetration testing, featuring:

- **ReAct Framework**: Reasoning and Acting capabilities with tool integration
- **Internet Search**: Real-time web search for latest CVEs, exploits, and techniques
- **Knowledge Base**: Vector database for storing and retrieving cybersecurity knowledge
- **Self-Improvement**: Periodic fine-tuning based on successful interactions
- **Local LLM**: Uses Llama 3 8B model for privacy and control

## Architecture

The system consists of several key components:

1. **Orchestrator**: Main agent loop that manages conversations and tool calls
2. **Tool System**: Modular tools for search, database operations, and more
3. **Vector Database**: ChromaDB for knowledge storage and retrieval
4. **LLM Integration**: Local Ollama integration with Llama 3 8B
5. **Logging System**: Comprehensive interaction logging for fine-tuning

## Quick Start

1. **Setup Environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Configure Settings**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and preferences
   ```

3. **Initialize Project**:
   ```bash
   python config.py
   ```

4. **Install Ollama and Llama 3**:
   ```bash
   # Install Ollama from https://ollama.ai
   ollama pull llama3:8b
   ```

5. **Run the Agent**:
   ```bash
   python -m src.core.cli
   ```

## Configuration

Key environment variables:

- `SERPER_API_KEY`: For web search functionality
- `OLLAMA_HOST`: Ollama server endpoint (default: http://localhost:11434)
- `LLM_MODEL`: Model to use (default: llama3:8b)
- `CHROMA_DB_PATH`: Vector database storage path

## Development

Run tests:
```bash
pytest tests/
```

Format code:
```bash
black src/ tests/
```

Type checking:
```bash
mypy src/
```

## License

MIT License - see LICENSE file for details.
