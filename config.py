"""Configuration management for Pentester AI Agent."""

import os
from pathlib import Path
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # API Keys
    serper_api_key: Optional[str] = Field(None, env="SERPER_API_KEY")
    google_search_api_key: Optional[str] = Field(None, env="GOOGLE_SEARCH_API_KEY")
    google_search_engine_id: Optional[str] = Field(None, env="GOOGLE_SEARCH_ENGINE_ID")
    
    # LLM Configuration
    ollama_host: str = Field("http://localhost:11434", env="OLLAMA_HOST")
    llm_model: str = Field("llama3:8b", env="LLM_MODEL")
    
    # Database Configuration
    chroma_db_path: str = Field("./data/chroma_db", env="CHROMA_DB_PATH")
    embedding_model: str = Field("all-MiniLM-L6-v2", env="EMBEDDING_MODEL")
    
    # Logging
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_file: str = Field("./logs/agent.log", env="LOG_FILE")
    
    # Agent Configuration
    max_context_length: int = Field(4096, env="MAX_CONTEXT_LENGTH")
    max_tool_calls_per_turn: int = Field(5, env="MAX_TOOL_CALLS_PER_TURN")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


def get_settings() -> Settings:
    """Get application settings."""
    return Settings()


def ensure_directories():
    """Ensure required directories exist."""
    directories = [
        "data",
        "data/chroma_db", 
        "logs",
        "tests",
        "src",
        "src/tools",
        "src/core",
        "src/utils"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)


if __name__ == "__main__":
    # Create directories and show current config
    ensure_directories()
    settings = get_settings()
    print("Configuration loaded:")
    print(f"LLM Model: {settings.llm_model}")
    print(f"Ollama Host: {settings.ollama_host}")
    print(f"ChromaDB Path: {settings.chroma_db_path}")
    print(f"Log Level: {settings.log_level}")
