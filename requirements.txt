# Core dependencies
requests>=2.31.0
python-dotenv>=1.0.0
pydantic>=2.0.0
pydantic-settings>=2.0.0
rich>=13.0.0
click>=8.1.0

# Vector Database and Embeddings
chromadb>=0.4.0
sentence-transformers>=2.2.0

# LLM Integration
ollama>=0.1.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0

# Development
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# Logging and Monitoring
structlog>=23.1.0
